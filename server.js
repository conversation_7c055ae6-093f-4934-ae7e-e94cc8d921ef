
const app = require('./app');
require('dotenv').config();
const http = require('http');
const socketHandler = require('./socket');
// You likely don't need the hourly PostingScheduler now if the every-second check covers it
// const PostingScheduler = require('./scheduler/postScheduler');
const generalScheduler = require('./services/scheduler'); // Keep if you still use schedule.js for other tasks
const postStatusChecker = require('./services/postStatusChecker'); // Import the new checker
const postStateChecker = require('./services/postState.checker');
const AdsScheduler = require('./scheduler/adsScheduler');
const EventScheduler = require('./scheduler/eventScheduler');
const EventReminderScheduler = require('./scheduler/eventReminderScheduler');
const PORT = process.env.PORT || 3000;

let serverInstance = null;
// let postingSchedulerTask = null; // Remove if you're no longer using the node-cron task


const gracefulShutdown = (exitCode = 0) => {
    console.log('Starting graceful shutdown...');

    // Stop the continuous post status checker first
    postStatusChecker.stop(); // Signal the checker loop to stop
    // The checker will log 'Post status checker loop stopping/exiting' when it's done its current cycle

    AdsScheduler.stop(); // stop cron job if running
    EventScheduler.stop(); // stop event scheduler
    EventReminderScheduler.stop(); // stop event reminder scheduler
    // // Stop the node-cron scheduler task if it was initialized (if still using it)
    // if (postingSchedulerTask) {
    //     console.log('Stopping hourly posting scheduler...');
    //     postingSchedulerTask.stop();
    //     console.log('Hourly posting scheduler stopped.');
    // }

    // node-schedule jobs will typically stop when the process exits.
    // If generalScheduler.initializeScheduledJobs() is used for other tasks, they'll stop here.
    console.log('Other scheduled jobs will terminate with process exit.');


    if (serverInstance) {
        console.log('Closing HTTP server...');
        serverInstance.close(() => {
            console.log('HTTP server closed.');
            // Only exit the process after critical resources (like the server and checkers) are closed
            process.exit(exitCode);
        });

        // Force close after timeout
        setTimeout(() => {
            console.error('Could not close connections in time, forcefully shutting down');
            process.exit(exitCode);
        }, 10000);
    } else {
        console.warn('No server instance to close.');
        process.exit(exitCode);
    }
};

// Handle process events
process.on('uncaughtException', (err) => {
    console.error('UNCAUGHT EXCEPTION! 💥 Shutting down...');
    console.error(err);
    gracefulShutdown(1);
});

process.on('unhandledRejection', (err) => {
    console.error('UNHANDLED REJECTION! 💥 Shutting down...');
    console.error(err);
    gracefulShutdown(1);
});

process.on('SIGTERM', () => {
    console.log('SIGTERM received. Shutting down gracefully...');
    gracefulShutdown(0);
});

process.on('SIGINT', () => {
    console.log('SIGINT received. Shutting down gracefully...');
    gracefulShutdown(0);
});


const startServer = async () => {
    try {
        // Start server
        serverInstance = app.listen(PORT, () => {
            console.log(`Server is running on port ${PORT}`);

            // --- Initialization Steps AFTER Server Starts ---

            // 1. Initialize Socket.IO with the actual server instance
            socketHandler.initialize(serverInstance);
            console.log('Socket.IO initialized.');

            // // 2. Initialize the hourly posting scheduler (if still needed)
            // // postingSchedulerTask = PostingScheduler.init({ schedule: '0 * * * *' });
            // // console.log('Hourly posting scheduler initialized.');

            // 3. Initialize general scheduled jobs from scheduler.js (if still needed)
            // generalScheduler.initializeScheduledJobs();
            // console.log('General scheduler jobs initialized.');

            // 4. Start the continuous post status checker
            postStatusChecker.start();
            postStateChecker.start(5000);
            console.log('Continuous post status checker started.');
            AdsScheduler.init();
            console.log('Ads scheduler initialized.');

            // 5. Initialize event schedulers
            EventScheduler.init();
            console.log('Event scheduler initialized.');
            EventReminderScheduler.init({ reminderDays: 3 });
            console.log('Event reminder scheduler initialized.');

            // --- End Initialization Steps ---
        });

        return serverInstance;

    } catch (error) {
        console.error('Error starting server:', error.message);
        gracefulShutdown(1);
    }
};

// Start the server if this file is executed directly
if (require.main === module) {
    startServer();
}

// Export for testing
module.exports = { startServer, gracefulShutdown, serverInstance: () => serverInstance };
