const cron = require('node-cron');
const eventsService = require('../services/events.service');

class EventScheduler {
  static task = null;

  static init(options = {}) {
    const schedule = '0 */10 * * * *'; // Every 10 minutes
    console.log(`EventScheduler: Initializing with schedule "${schedule}"`);
    
    EventScheduler.task = cron.schedule(schedule, async () => {
      try {
        console.log('EventScheduler: Running scheduled event status update...');
        
        // Update events that should be active now
        const activatedCount = await eventsService.updateActiveEvents();
        if (activatedCount > 0) {
          console.log(`EventScheduler: Activated ${activatedCount} events`);
        }
        
        // Update events that should be expired now
        const expiredCount = await eventsService.updateExpiredEvents();
        if (expiredCount > 0) {
          console.log(`EventScheduler: Expired ${expiredCount} events`);
        }
        
      } catch (error) {
        console.error('EventScheduler: Error running scheduled task:', error);
      }
    });

    return EventScheduler.task;
  }

  static stop() {
    if (EventScheduler.task) {
      EventScheduler.task.stop();
      console.log('Event scheduler stopped.');
    }
  }
}

module.exports = EventScheduler;
