const cron = require('node-cron');
const eventsService = require('../services/events.service');
const sendNotification = require('../services/sendNotficationService');

class EventReminderScheduler {
  static task = null;
  static sentReminders = new Set(); // Track sent reminders to avoid duplicates

  static init(options = {}) {
    const schedule = '0 0 9 * * *'; // Every day at 9:00 AM
    const reminderDays = options.reminderDays || 3; // Default 3 days before event ends
    
    console.log(`EventReminderScheduler: Initializing with schedule "${schedule}" for ${reminderDays} days ahead`);
    
    EventReminderScheduler.task = cron.schedule(schedule, async () => {
      try {
        console.log('EventReminderScheduler: Checking for events ending soon...');
        
        // Get events ending within the specified number of days
        const eventsEndingSoon = await eventsService.getEventsEndingSoon(reminderDays);
        
        for (const event of eventsEndingSoon) {
          const reminderKey = `${event.id}_${new Date(event.end_date).toDateString()}`;
          
          // Check if we've already sent a reminder for this event on this date
          if (!EventReminderScheduler.sentReminders.has(reminderKey)) {
            const endDate = new Date(event.end_date);
            const daysLeft = Math.ceil((endDate - new Date()) / (1000 * 60 * 60 * 24));
            
            const MAX_TITLE_LENGTH = 60;
            const MAX_BODY_LENGTH = 150;
            
            const title = event.title && event.title.length > MAX_TITLE_LENGTH
              ? event.title.slice(0, MAX_TITLE_LENGTH - 3) + "..."
              : event.title || "Event Reminder";
            
            const body = `${daysLeft} day${daysLeft > 1 ? 's' : ''} left for "${title}". Don't miss out!`;
            const truncatedBody = body.length > MAX_BODY_LENGTH
              ? body.slice(0, MAX_BODY_LENGTH - 3) + "..."
              : body;
            
            await sendNotification.sendToAllViaTopic(
              `Event Reminder: ${title}`,
              truncatedBody
            );
            
            // Mark this reminder as sent
            EventReminderScheduler.sentReminders.add(reminderKey);
            
            console.log(`EventReminderScheduler: Sent reminder for event "${event.title}" (${daysLeft} days left)`);
          }
        }
        
        // Clean up old reminder keys (older than 7 days) to prevent memory leaks
        EventReminderScheduler.cleanupOldReminders();
        
      } catch (error) {
        console.error('EventReminderScheduler: Error running scheduled task:', error);
      }
    });

    return EventReminderScheduler.task;
  }

  static cleanupOldReminders() {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const keysToRemove = [];
    for (const key of EventReminderScheduler.sentReminders) {
      const dateStr = key.split('_')[1];
      const keyDate = new Date(dateStr);
      if (keyDate < sevenDaysAgo) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => EventReminderScheduler.sentReminders.delete(key));
    
    if (keysToRemove.length > 0) {
      console.log(`EventReminderScheduler: Cleaned up ${keysToRemove.length} old reminder keys`);
    }
  }

  static stop() {
    if (EventReminderScheduler.task) {
      EventReminderScheduler.task.stop();
      console.log('Event reminder scheduler stopped.');
    }
  }

  static clearReminders() {
    EventReminderScheduler.sentReminders.clear();
    console.log('Event reminder cache cleared.');
  }
}

module.exports = EventReminderScheduler;
