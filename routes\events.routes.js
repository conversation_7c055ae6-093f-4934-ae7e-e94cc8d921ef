const express = require('express');
const router = express.Router();
const eventsController = require('../controllers/events.controller');
const multer = require('multer');
const fs = require('fs');

// Setup multer storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = 'uploads/';

    // Create the directory if it doesn't exist
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({ storage });

router.post('/', upload.single('image'), eventsController.createEvent);
router.get('/', eventsController.getEvents);
router.get('/:id', eventsController.getEventById);
router.put('/:id', upload.single('image'), eventsController.updateEvent);
router.delete('/:id', eventsController.deleteEvent);

module.exports = router;
